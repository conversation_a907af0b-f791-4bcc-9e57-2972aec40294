{{/* 改进版本的 Meta Description 实现 */}}
{{/* 
综合两种方案优点的改进版本：
1. 优先使用 front matter 中的 description 字段
2. 其次使用 front matter 中的 summary 字段  
3. 对于页面和分类页面，截取内容前150个字符
4. 对于其他页面类型，使用 "标题 - 站点名" 格式
5. 最后使用站点默认描述
*/}}
<meta
  name="description"
  content="{{ with .Description }}
    {{- . -}}
  {{ else }}
    {{ with .Params.summary }}
      {{- . -}}
    {{ else }}
      {{ if or .IsPage .IsSection }}
        {{ if .Content }}
          {{- .Content | plainify | truncate 150 "..." -}}
        {{ else }}
          {{- printf "%s - %s" .Title site.Title -}}
        {{ end }}
      {{ else }}
        {{- with site.Params.site.description }}{{ . }}{{ else }}{{ printf "%s - %s" .Title site.Title }}{{ end -}}
      {{ end }}
    {{ end }}
  {{ end }}" />

{{/* 参考方案的改进版本 */}}
{{/* 
<meta name="description" content="{{- with .Description }}{{ . }}{{- else }}{{- if or .IsPage .IsSection}}
    {{- .Summary | plainify | truncate 150 "..." | default (printf "%s - %s" .Title site.Title) }}{{- else }}
    {{- with site.Params.description }}{{ . }}{{- end }}{{- end }}{{- end -}}">
*/}}

{{/* 最佳实践版本 - 结合两种方案的优点 */}}
{{/*
<meta
  name="description"
  content="{{ with .Description }}
    {{- . | truncate 160 "..." -}}
  {{ else }}
    {{ with .Params.summary }}
      {{- . | truncate 160 "..." -}}
    {{ else }}
      {{ if or .IsPage .IsSection }}
        {{ if .Content }}
          {{- .Content | plainify | truncate 150 "..." -}}
        {{ else }}
          {{- .Summary | plainify | truncate 150 "..." | default (printf "%s - %s" .Title site.Title) -}}
        {{ end }}
      {{ else }}
        {{- with site.Params.site.description }}{{ . }}{{ else }}{{ printf "%s - %s" .Title site.Title }}{{ end -}}
      {{ end }}
    {{ end }}
  {{ end }}" />
*/}}
