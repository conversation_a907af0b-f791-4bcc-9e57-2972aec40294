---
title: "Hugo多语言导航测试"
date: 2024-01-18T09:15:00+08:00
draft: false
description: "专门用于测试Hugo多语言网站中各种导航功能的文章。"
categories: ["测试", "导航"]
tags: ["Hugo", "导航", "多语言", "测试", "链接"]
translationKey: "navigation-test"
---

# Hugo多语言导航测试

这篇文章专门用于测试Hugo多语言网站中的各种导航功能。

## 测试清单

请按照以下步骤进行测试：

### 1. 基础导航测试

- [ ] 点击顶部导航栏的"文章"链接
- [ ] 点击顶部导航栏的"分类"链接  
- [ ] 点击顶部导航栏的"标签"链接
- [ ] 点击顶部导航栏的"归档"链接

### 2. 语言切换测试

- [ ] 切换到英语，检查URL是否正确
- [ ] 切换到日语，检查URL是否包含 `/ja/`
- [ ] 切换到法语，检查URL是否包含 `/fr/`
- [ ] 切换回中文，检查URL是否包含 `/zh-cn/`

### 3. 移动端测试

- [ ] 在移动设备上打开菜单
- [ ] 测试移动端的语言切换功能
- [ ] 测试移动端的导航链接

### 4. 面包屑导航测试

- [ ] 点击面包屑中的"首页"链接
- [ ] 检查面包屑是否显示正确的路径

### 5. 分页导航测试

如果有多页内容：
- [ ] 点击"下一页"按钮
- [ ] 点击"上一页"按钮
- [ ] 点击具体页码

### 6. 文章内导航测试

- [ ] 测试文章间的"上一篇"/"下一篇"导航
- [ ] 测试相关文章链接

## 预期结果

所有测试项目都应该：

1. **保持语言状态**：切换语言后，所有链接都应该保持当前语言
2. **URL正确性**：
   - 中文页面URL应包含 `/zh-cn/`
   - 英文页面URL不应包含语言前缀（默认语言）
   - 其他语言页面URL应包含对应的语言前缀
3. **功能完整性**：所有导航功能都应该正常工作

## 常见问题排查

如果遇到问题，请检查：

### 问题1：点击链接后语言切换回英文
**可能原因**：某些链接没有使用语言感知的URL生成函数
**解决方法**：检查模板文件中是否使用了 `relLangURL` 或 `RelPermalink`

### 问题2：某些页面404错误
**可能原因**：对应语言的内容文件不存在
**解决方法**：为每种语言创建对应的内容文件

### 问题3：i18n翻译不显示
**可能原因**：i18n文件缺失或语言代码不匹配
**解决方法**：检查 `i18n/` 目录下的语言文件

## 技术说明

### URL结构说明

- **英文（默认语言）**：`/posts/`, `/categories/`
- **中文**：`/zh-cn/posts/`, `/zh-cn/categories/`
- **日语**：`/ja/posts/`, `/ja/categories/`
- **其他语言**：`/{language-code}/posts/`

### 模板函数说明

- `relLangURL`：生成包含语言前缀的相对URL
- `RelPermalink`：生成页面的语言感知永久链接
- `site.GetPage`：获取指定页面的语言版本

## 测试完成

如果所有测试项目都通过，说明Hugo多语言功能配置正确！

---

**注意**：请在测试过程中记录任何异常情况，以便进一步优化配置。
